<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test isTossFinish Flag</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        canvas {
            border: 2px solid #ddd;
            border-radius: 10px;
            display: block;
            margin: 20px auto;
        }
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 12px 24px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #45a049;
        }
        button:disabled {
            background: #cccccc;
            cursor: not-allowed;
        }
        .status {
            background: #f0f0f0;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            font-family: monospace;
        }
        .status-item {
            margin: 5px 0;
        }
        .true { color: #4CAF50; font-weight: bold; }
        .false { color: #f44336; font-weight: bold; }
        .log {
            background: #000;
            color: #0f0;
            padding: 10px;
            border-radius: 5px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🪙 Test isTossFinish Flag</h1>
        <p>This test demonstrates the <code>isTossFinish</code> flag that marks when all coin flip scenes are complete.</p>
        
        <canvas id="coinCanvas" width="400" height="400"></canvas>
        
        <div class="controls">
            <button id="tossBtn">Toss Coin</button>
            <button id="tossWinBtn">Toss with WIN Scene</button>
            <button id="tossLoseBtn">Toss with LOSE Scene</button>
            <button id="idleBtn">Start Idle</button>
        </div>
        
        <div class="status">
            <h3>Status Monitor:</h3>
            <div class="status-item">isFlipping: <span id="isFlipping" class="false">false</span></div>
            <div class="status-item">isIdle: <span id="isIdle" class="false">false</span></div>
            <div class="status-item">isTossComplete: <span id="isTossComplete" class="false">false</span></div>
        </div>
        
        <div class="log" id="log"></div>
    </div>

    <!-- Three.js CDN -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <!-- CoinFlipper Module -->
    <script src="src/coin-flipper.js"></script>
    
    <script>
        let coinFlipper;
        let statusInterval;
        
        // Initialize
        async function init() {
            try {
                coinFlipper = new CoinFlipper('coinCanvas', {
                    enableSound: true,
                    flipDuration: 3000
                });
                
                await coinFlipper.ready();
                log('✅ CoinFlipper initialized successfully');
                
                // Start status monitoring
                startStatusMonitoring();
                
                // Start idle animation
                coinFlipper.startIdle();
                log('🔄 Started idle animation');
                
            } catch (error) {
                log('❌ Failed to initialize: ' + error.message);
            }
        }
        
        // Status monitoring
        function startStatusMonitoring() {
            statusInterval = setInterval(() => {
                if (coinFlipper) {
                    const status = coinFlipper.status;
                    updateStatusDisplay('isFlipping', status.isFlipping);
                    updateStatusDisplay('isIdle', status.isIdle);
                    updateStatusDisplay('isTossComplete', status.isTossComplete);
                }
            }, 100);
        }
        
        function updateStatusDisplay(id, value) {
            const element = document.getElementById(id);
            element.textContent = value.toString();
            element.className = value ? 'true' : 'false';
        }
        
        // Logging
        function log(message) {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        // Event handlers
        document.getElementById('tossBtn').addEventListener('click', async () => {
            if (!coinFlipper) return;
            
            log('🎯 Starting normal toss...');
            try {
                const result = await coinFlipper.toss();
                log(`🎯 Toss completed with result: ${result}`);
            } catch (error) {
                log(`❌ Toss failed: ${error.message}`);
            }
        });
        
        document.getElementById('tossWinBtn').addEventListener('click', async () => {
            if (!coinFlipper) return;
            
            log('🎯 Starting toss with WIN scene...');
            try {
                const result = await coinFlipper.toss('heads', 'win');
                log(`🎯 WIN toss completed with result: ${result}`);
            } catch (error) {
                log(`❌ WIN toss failed: ${error.message}`);
            }
        });
        
        document.getElementById('tossLoseBtn').addEventListener('click', async () => {
            if (!coinFlipper) return;
            
            log('🎯 Starting toss with LOSE scene...');
            try {
                const result = await coinFlipper.toss('tails', 'lose');
                log(`🎯 LOSE toss completed with result: ${result}`);
            } catch (error) {
                log(`❌ LOSE toss failed: ${error.message}`);
            }
        });
        
        document.getElementById('idleBtn').addEventListener('click', () => {
            if (!coinFlipper) return;
            
            log('🔄 Starting idle animation...');
            coinFlipper.startIdle();
        });
        
        // Initialize when page loads
        window.addEventListener('load', init);
        
        // Cleanup
        window.addEventListener('beforeunload', () => {
            if (statusInterval) {
                clearInterval(statusInterval);
            }
        });
    </script>
</body>
</html>
