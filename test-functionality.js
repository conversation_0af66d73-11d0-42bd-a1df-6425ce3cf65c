// Simple functionality test for win/lose scene features
const fs = require('fs');

console.log('🧪 Testing Win/Lose Scene Functionality');
console.log('=====================================');

try {
    // Read the coin-flipper.js file
    const jsContent = fs.readFileSync('src/coin-flipper.js', 'utf8');
    
    // Test 1: Check if win/lose text creation function exists
    const hasCreateWinLoseText = jsContent.includes('createWinLoseText(winLoseState)');
    console.log('✅ Test 1 - Win/Lose text creation function:', hasCreateWinLoseText ? 'PASS' : 'FAIL');
    
    // Test 2: Check if confetti effect function exists
    const hasCreateConfettiEffect = jsContent.includes('createConfettiEffect()');
    console.log('✅ Test 2 - Confetti effect function:', hasCreateConfettiEffect ? 'PASS' : 'FAIL');
    
    // Test 3: Check if cleanup function exists
    const hasCleanupWinLoseScene = jsContent.includes('cleanupWinLoseScene()');
    console.log('✅ Test 3 - Cleanup function:', hasCleanupWinLoseScene ? 'PASS' : 'FAIL');
    
    // Test 4: Check if forceFlat accepts winLoseState parameter
    const hasForceFlatWithParam = jsContent.includes('forceFlat(winLoseState = null)');
    console.log('✅ Test 4 - forceFlat with winLoseState param:', hasForceFlatWithParam ? 'PASS' : 'FAIL');
    
    // Test 5: Check if flipCoin accepts winLoseState and playSound parameters
    const hasFlipCoinWithParam = jsContent.includes('flipCoin(result = null, winLoseState = null, playSound = true)');
    console.log('✅ Test 5 - flipCoin with winLoseState and playSound params:', hasFlipCoinWithParam ? 'PASS' : 'FAIL');
    
    // Test 6: Check if toss method has been updated
    const hasTossWithWinLose = jsContent.includes('playSoundOrWinLose') || jsContent.includes('winLoseState');
    console.log('✅ Test 6 - toss method updated for win/lose:', hasTossWithWinLose ? 'PASS' : 'FAIL');
    
    // Test 7: Check if WIN text is created with green color
    const hasWinTextGreen = jsContent.includes('#00ff00') && jsContent.includes('WIN!!!');
    console.log('✅ Test 7 - WIN text with green color:', hasWinTextGreen ? 'PASS' : 'FAIL');
    
    // Test 8: Check if LOSE text is created with red color
    const hasLoseTextRed = jsContent.includes('#ff0000') && jsContent.includes('LOSE');
    console.log('✅ Test 8 - LOSE text with red color:', hasLoseTextRed ? 'PASS' : 'FAIL');
    
    // Test 9: Check if confetti particles are created
    const hasConfettiParticles = jsContent.includes('confettiParticles') && jsContent.includes('BoxGeometry');
    console.log('✅ Test 9 - Confetti particles creation:', hasConfettiParticles ? 'PASS' : 'FAIL');
    
    // Test 10: Check if confetti only plays on win
    const hasWinOnlyConfetti = jsContent.includes("winLoseState === 'win'") && jsContent.includes('createConfettiEffect');
    console.log('✅ Test 10 - Confetti only on win:', hasWinOnlyConfetti ? 'PASS' : 'FAIL');
    
    console.log('\n📊 Summary:');
    const tests = [
        hasCreateWinLoseText,
        hasCreateConfettiEffect,
        hasCleanupWinLoseScene,
        hasForceFlatWithParam,
        hasFlipCoinWithParam,
        hasTossWithWinLose,
        hasWinTextGreen,
        hasLoseTextRed,
        hasConfettiParticles,
        hasWinOnlyConfetti
    ];
    
    const passedTests = tests.filter(test => test).length;
    const totalTests = tests.length;
    
    console.log(`Passed: ${passedTests}/${totalTests} tests`);
    console.log(`Success Rate: ${Math.round((passedTests / totalTests) * 100)}%`);
    
    if (passedTests === totalTests) {
        console.log('🎉 All tests passed! Win/Lose scene functionality is implemented correctly.');
    } else {
        console.log('⚠️  Some tests failed. Please check the implementation.');
    }
    
} catch (error) {
    console.error('❌ Error running tests:', error.message);
}

// Test demo page
console.log('\n🌐 Testing Demo Page');
console.log('===================');

try {
    const htmlContent = fs.readFileSync('demo/test.html', 'utf8');
    
    // Check if win/lose buttons exist
    const hasWinButtons = htmlContent.includes('winHeadsBtn') && htmlContent.includes('winTailsBtn');
    const hasLoseButtons = htmlContent.includes('loseHeadsBtn') && htmlContent.includes('loseTailsBtn');
    const hasTestWinLoseFunction = htmlContent.includes('testWinLoseFlip');
    
    console.log('✅ Win buttons in demo:', hasWinButtons ? 'PASS' : 'FAIL');
    console.log('✅ Lose buttons in demo:', hasLoseButtons ? 'PASS' : 'FAIL');
    console.log('✅ Test function in demo:', hasTestWinLoseFunction ? 'PASS' : 'FAIL');
    
    const demoTests = [hasWinButtons, hasLoseButtons, hasTestWinLoseFunction];
    const demoPassed = demoTests.filter(test => test).length;
    
    console.log(`Demo tests passed: ${demoPassed}/${demoTests.length}`);
    
} catch (error) {
    console.error('❌ Error testing demo page:', error.message);
}

console.log('\n✨ Testing complete!');
