// Test for sound parameter fix
const fs = require('fs');

console.log('🔊 Testing Sound Parameter Fix');
console.log('==============================');

try {
    // Read the coin-flipper.js file
    const jsContent = fs.readFileSync('src/coin-flipper.js', 'utf8');
    
    // Test 1: Check if toss method passes playSound to flipCoin
    const tossPassing = jsContent.includes('this.coinRenderer.flipCoin(result, finalWinLoseState, playSound)');
    console.log('✅ Test 1 - toss passes playSound to flipCoin:', tossPassing ? 'PASS' : 'FAIL');
    
    // Test 2: Check if flipCoin accepts playSound parameter
    const flipCoinParam = jsContent.includes('async flipCoin(result = null, winLoseState = null, playSound = true)');
    console.log('✅ Test 2 - flipCoin accepts playSound parameter:', flipCoinParam ? 'PASS' : 'FAIL');
    
    // Test 3: Check if playSoundEnabled is stored in flipCoin
    const soundStored = jsContent.includes('this.playSoundEnabled = playSound');
    console.log('✅ Test 3 - playSoundEnabled is stored:', soundStored ? 'PASS' : 'FAIL');
    
    // Test 4: Check if playSoundEnabled is initialized in constructor
    const soundInitialized = jsContent.includes('this.playSoundEnabled = true');
    console.log('✅ Test 4 - playSoundEnabled initialized in constructor:', soundInitialized ? 'PASS' : 'FAIL');
    
    // Test 5: Check if win sound respects playSoundEnabled flag
    const winSoundCheck = jsContent.includes('if (this.audioManager && this.playSoundEnabled)') && 
                         jsContent.includes('generateWinSound()');
    console.log('✅ Test 5 - Win sound checks playSoundEnabled:', winSoundCheck ? 'PASS' : 'FAIL');
    
    // Test 6: Check if lose sound respects playSoundEnabled flag
    const loseSoundCheck = jsContent.includes('if (this.audioManager && this.playSoundEnabled)') && 
                          jsContent.includes('generateLoseSound()');
    console.log('✅ Test 6 - Lose sound checks playSoundEnabled:', loseSoundCheck ? 'PASS' : 'FAIL');
    
    // Test 7: Check if there are debug messages for disabled sounds
    const debugWin = jsContent.includes('Win sound disabled by playSoundEnabled flag');
    const debugLose = jsContent.includes('Lose sound disabled by playSoundEnabled flag');
    console.log('✅ Test 7 - Debug messages for disabled sounds:', (debugWin && debugLose) ? 'PASS' : 'FAIL');
    
    // Test 8: Check if flipCoin documentation is updated
    const docUpdated = jsContent.includes('@param {boolean} playSound - เล่นเสียงหรือไม่ (รวมถึงเสียง win/lose)');
    console.log('✅ Test 8 - flipCoin documentation updated:', docUpdated ? 'PASS' : 'FAIL');
    
    console.log('\n📊 Summary:');
    const tests = [
        tossPassing,
        flipCoinParam,
        soundStored,
        soundInitialized,
        winSoundCheck,
        loseSoundCheck,
        (debugWin && debugLose),
        docUpdated
    ];
    
    const passedTests = tests.filter(test => test).length;
    const totalTests = tests.length;
    
    console.log(`Passed: ${passedTests}/${totalTests} tests`);
    console.log(`Success Rate: ${Math.round((passedTests / totalTests) * 100)}%`);
    
    if (passedTests === totalTests) {
        console.log('🎉 All tests passed! Sound parameter fix is implemented correctly.');
        console.log('✅ When playSoundOrWinLose is false, ALL sounds (toss + win/lose) will be disabled.');
    } else {
        console.log('⚠️  Some tests failed. Please check the implementation.');
    }
    
} catch (error) {
    console.error('❌ Error running tests:', error.message);
}

console.log('\n🔍 Code Analysis:');
console.log('=================');

try {
    const jsContent = fs.readFileSync('src/coin-flipper.js', 'utf8');
    
    // Find the toss method and analyze the sound parameter handling
    const tossMethodMatch = jsContent.match(/async toss\(.*?\) \{[\s\S]*?return finalResult;[\s\S]*?\}/);
    if (tossMethodMatch) {
        const tossMethod = tossMethodMatch[0];
        
        // Check if toss sound is properly controlled
        const tossSoundControlled = tossMethod.includes('if (playSound && this.audioManager)');
        console.log('✅ Toss sound controlled by playSound parameter:', tossSoundControlled ? 'YES' : 'NO');
        
        // Check if win/lose sounds are passed through
        const winLosePassed = tossMethod.includes('flipCoin(result, finalWinLoseState, playSound)');
        console.log('✅ Win/lose sounds parameter passed through:', winLosePassed ? 'YES' : 'NO');
    }
    
    // Find showWinLoseScene method and analyze sound control
    const showWinLoseMatch = jsContent.match(/showWinLoseScene\(winLoseState\) \{[\s\S]*?\}/);
    if (showWinLoseMatch) {
        const showWinLoseMethod = showWinLoseMatch[0];
        
        // Check if win/lose sounds are controlled
        const winLoseSoundControlled = showWinLoseMethod.includes('this.playSoundEnabled');
        console.log('✅ Win/lose sounds controlled by playSoundEnabled:', winLoseSoundControlled ? 'YES' : 'NO');
    }
    
} catch (error) {
    console.error('❌ Error analyzing code:', error.message);
}

console.log('\n✨ Testing complete!');
