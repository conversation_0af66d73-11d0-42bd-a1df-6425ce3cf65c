<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Coin Flipper Idle Speed Comparison</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 10px; background: #f5f5f5; padding: 0; }
        .container { background: white; padding: 15px; border-radius: 10px; max-width: 1200px; margin: 0 auto; box-shadow: 0 4px 15px rgba(0,0,0,0.1); }
        h1 { text-align: center; color: #2c3e50; margin-bottom: 10px; font-size: 24px; }
        .subtitle { text-align: center; color: #7f8c8d; margin-bottom: 20px; font-size: 14px; }
        .coins-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin: 20px 0; }
        .coin-container { display: flex; flex-direction: column; align-items: center; padding: 15px; background: linear-gradient(135deg, #2c3e50, #34495e); border-radius: 10px; box-shadow: inset 0 2px 10px rgba(0,0,0,0.3); }
        .coin-canvas { border: 2px solid #ecf0f1; border-radius: 8px; box-shadow: 0 4px 8px rgba(0,0,0,0.2); max-width: 100%; height: auto; }
        .coin-label { color: white; font-weight: bold; font-size: 16px; margin-bottom: 10px; text-align: center; }
        .coin-status { color: #ecf0f1; font-size: 12px; margin-top: 10px; text-align: center; }
        
        /* Mobile responsive styles */
        @media (max-width: 768px) {
            body { margin: 5px; }
            .container { padding: 10px; margin: 5px; }
            h1 { font-size: 20px; margin-bottom: 8px; }
            .subtitle { font-size: 12px; margin-bottom: 15px; }
            .coins-grid { gap: 10px; margin: 15px 0; }
            .coin-container { padding: 10px; }
            .coin-label { font-size: 14px; margin-bottom: 8px; }
            .coin-status { font-size: 11px; margin-top: 8px; }
            canvas { width: 100% !important; height: auto !important; max-width: 250px; }
        }
        
        @media (max-width: 480px) {
            .coins-grid { grid-template-columns: 1fr; gap: 15px; }
            canvas { max-width: 280px; }
            h1 { font-size: 18px; }
            .coin-label { font-size: 16px; }
        }
        .controls { text-align: center; margin: 25px 0; }
        .btn { padding: 15px 30px; margin: 10px; border: none; border-radius: 8px; background: linear-gradient(135deg, #3498db, #2980b9); color: white; cursor: pointer; font-size: 16px; font-weight: 600; transition: all 0.3s ease; box-shadow: 0 4px 8px rgba(0,0,0,0.2); }
        .btn:hover { transform: translateY(-2px); box-shadow: 0 6px 12px rgba(0,0,0,0.3); }
        .btn:disabled { background: #bdc3c7; cursor: not-allowed; transform: none; box-shadow: none; }
        .btn.speed-btn { background: linear-gradient(135deg, #9b59b6, #8e44ad); font-size: 14px; padding: 12px 25px; }
        .btn.speed-btn.active { background: linear-gradient(135deg, #27ae60, #2ecc71); }
        .btn.custom-speed { background: linear-gradient(135deg, #f39c12, #e67e22); font-size: 14px; padding: 12px 25px; }
        .speed-section { background: #ecf0f1; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .speed-section h3 { margin-top: 0; color: #2c3e50; text-align: center; }
        .speed-presets { display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 10px; margin: 15px 0; }
        .custom-speed-controls { display: flex; align-items: center; justify-content: center; gap: 15px; margin: 15px 0; }
        .speed-input { padding: 8px 12px; border: 2px solid #bdc3c7; border-radius: 5px; font-size: 16px; width: 100px; text-align: center; }
        .current-speed { background: #3498db; color: white; padding: 10px 20px; border-radius: 5px; font-weight: bold; text-align: center; margin: 10px 0; }
        .comparison-note { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 8px; margin: 20px 0; color: #856404; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🪙 Coin Flipper Idle Speed Comparison</h1>
        <p class="subtitle">เปรียบเทียบความเร็วการหมุน Idle Animation แบบ 4 หน้าต่างพร้อมกัน</p>
        
        <div class="coins-grid">
            <div class="coin-container">
                <div class="coin-label">Speed: 0.08 (Slowest)</div>
                <canvas id="coinCanvas1" width="300" height="300" class="coin-canvas"></canvas>
                <div class="coin-status" id="status1">กำลังโหลด...</div>
            </div>
            
            <div class="coin-container">
                <div class="coin-label">Speed: 0.1 (Slow)</div>
                <canvas id="coinCanvas2" width="300" height="300" class="coin-canvas"></canvas>
                <div class="coin-status" id="status2">กำลังโหลด...</div>
            </div>
            
            <div class="coin-container">
                <div class="coin-label">Speed: 0.3 (Medium)</div>
                <canvas id="coinCanvas3" width="300" height="300" class="coin-canvas"></canvas>
                <div class="coin-status" id="status3">กำลังโหลด...</div>
            </div>
            
            <div class="coin-container">
                <div class="coin-label">Speed: 0.6 (Fast)</div>
                <canvas id="coinCanvas4" width="300" height="300" class="coin-canvas"></canvas>
                <div class="coin-status" id="status4">กำลังโหลด...</div>
            </div>
        </div>
        
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="../src/coin-flipper.js"></script>
    
    <script>
        let coinFlippers = [];
        let currentSpeedElement = document.getElementById('currentSpeed');
        let changeableSpeed = 0.08;

        // Fixed speeds for all 4 coins
        const fixedSpeeds = [0.08, 0.1, 0.3, 0.6];

        function log(message) {
            console.log(message);
        }

        function updateStatus(coinIndex, message, isSuccess = null) {
            const statusElement = document.getElementById(`status${coinIndex + 1}`);
            if (statusElement) {
                statusElement.textContent = message;
                statusElement.style.color = isSuccess === true ? '#2ecc71' : 
                                           isSuccess === false ? '#e74c3c' : '#ecf0f1';
            }
        }

        async function initCoin(index, speed) {
            try {
                const canvasId = `coinCanvas${index + 1}`;
                log(`เริ่มต้น CoinFlipper ${index + 1} (Speed: ${speed})...`);
                updateStatus(index, 'กำลังโหลด...');
                
                const coinFlipper = new CoinFlipper(canvasId, {
                    idleSpeed: speed,
                    flipDuration: 2000,
                    enableSound: index === 0 // Only enable sound for first coin to avoid overlap
                });

                await coinFlipper.ready();
                log(`CoinFlipper ${index + 1} พร้อมใช้งาน!`);

                await coinFlipper.startIdle();
                log(`เริ่ม idle animation ${index + 1} ด้วยความเร็ว ${speed}`);
                updateStatus(index, `Speed: ${speed}`, true);
                
                coinFlippers[index] = coinFlipper;

            } catch (error) {
                log(`ข้อผิดพลาดในการสร้าง CoinFlipper ${index + 1}: ${error.message}`);
                updateStatus(index, 'ข้อผิดพลาด', false);
            }
        }

        async function init() {
            try {
                // Initialize all 4 coins with fixed speeds
                const initPromises = [];
                
                for (let i = 0; i < 4; i++) {
                    initPromises.push(initCoin(i, fixedSpeeds[i]));
                }
                
                // Wait for all coins to initialize
                await Promise.all(initPromises);
                
                log('CoinFlippers ทั้งหมดพร้อมใช้งาน!');

            } catch (error) {
                log('ข้อผิดพลาด: ' + error.message);
            }
        }

        window.addEventListener('load', init);
    </script>
</body>
</html>