# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a 3D Coin Flipper Module for Vue.js that provides realistic coin flipping animations with Three.js integration and audio effects. The module is self-contained and can be imported directly into Vue.js projects.

## Development Commands

### Testing
```bash
# Open demo in browser for testing
npm run demo

# Run basic functionality test
npm test
```

### Demo Testing
Open `demo/test.html` or other HTML files in `demo/` directory in browser to test functionality:
- `demo/test.html` - Main demo
- `demo/minimal-test.html` - Minimal implementation
- `demo/collision-test.html` - Collision testing
- `demo/confetti-test.html` - Confetti effects test

## Architecture

### Core Components

1. **CoinFlipper Class** (`src/coin-flipper.js`) - Main module class that orchestrates audio and rendering
2. **AudioManager Class** - Handles sound effects (coin throw, bounce, win/lose sounds)
3. **CoinRenderer Class** - Manages 3D animations using Three.js with idle and toss animations
4. **TypeScript Definitions** (`src/coin-flipper.d.ts`) - Complete type definitions for TypeScript projects

### Key Features

- **Idle Animation**: Slow spinning animation when coin is not being flipped
- **Toss Animation**: Realistic physics-based coin flipping with bounce effects
- **Audio System**: Real-time audio with throw sounds and bounce feedback
- **Win/Lose States**: Visual and audio feedback for game outcomes
- **Three.js Integration**: Automatic loading of Three.js from CDN or npm package

### Module Structure

The module uses a self-contained approach where Three.js is loaded dynamically:
- Browser: Loads from window.THREE or CDN
- Node.js: Requires 'three' package
- Fallback with warning if Three.js unavailable

### Vue.js Integration

The module is designed specifically for Vue.js with:
- Reactive status properties
- Promise-based async methods
- Proper cleanup with `destroy()` method
- Canvas ref binding support

## Common Usage Patterns

### Basic Setup
```javascript
const coinFlipper = new CoinFlipper(canvasElement, options)
await coinFlipper.ready()
await coinFlipper.startIdle()
```

### Flipping with Results
```javascript
// Random result
const result = await coinFlipper.toss()

// Forced result
const result = await coinFlipper.toss('heads')

// With win/lose state
const result = await coinFlipper.toss('heads', 'win')
```

### Cleanup
```javascript
// Always call destroy() when component unmounts
coinFlipper.destroy()
```

## File Structure Notes

- `src/` - Main module files (JS and TypeScript definitions)
- `examples/` - Vue.js component examples (JS and TypeScript variants)
- `demo/` - HTML test files for development and testing
- `docs/` - Detailed documentation
- `images/` - Coin texture assets (PNG and base64 encoded)
- Various `.md` files contain feature documentation and changelogs

## Testing Strategy

Use the HTML demo files in the `demo/` directory for testing rather than automated tests. The module requires browser environment for Three.js and Web Audio API functionality.